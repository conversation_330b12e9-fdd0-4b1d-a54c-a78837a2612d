<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

use Magento\Framework\View\Element\Template;

/**
 * @var $block \Magento\Shipping\Block\Tracking\Popup
 * @var \Magento\Framework\View\Helper\SecureHtmlRenderer $secureRenderer
 */

$results = $block->getTrackingInfo();
?>
<div class="page tracking">
    <?php if (!empty($results)): ?>
        <?php foreach ($results as $shipId => $result): ?>
            <?php if ($shipId): ?>
                <div class="order subtitle caption">
                    <?= /* @noEscape */ $block->escapeHtml(__('Shipment #')) . $shipId ?>
                </div>
            <?php endif; ?>
            <?php if (!empty($result)): ?>
                <?php foreach ($result as $counter => $track): ?>
                    <div class="table-wrapper">
                        <?php
                        $number = is_object($track) ? $track->getTracking() : $track['number'];
                        $fields = [
                            'Status' => 'getStatus',
                            'Signed by' => 'getSignedby',
                            'Delivered to' => 'getDeliveryLocation',
                            'Shipped or billed on' => 'getShippedDate',
                            'Service Type' => 'getService',
                            'Weight' => 'getWeight',
                        ];
                        ?>
                        <table class="data table order tracking" id="tracking-table-popup-<?= $block->escapeHtml($number) ?>">
                            <caption class="table-caption"><?= $block->escapeHtml(__('Order tracking')) ?></caption>
                            <tbody>
                            <?php if (is_object($track)) : ?>
                                <tr>
                                    <th class="col label" scope="row"><?= $block->escapeHtml(__('Tracking Number:')) ?></th>
                                    <td class="col value"><?= $block->escapeHtml($number) ?></td>
                                </tr>
                                <?php if ($track->getCarrierTitle()) : ?>
                                    <tr>
                                        <th class="col label" scope="row"><?= $block->escapeHtml(__('Carrier:')) ?></th>
                                        <td class="col value"><?= $block->escapeHtml($track->getCarrierTitle()) ?></td>
                                    </tr>
                                <?php endif; ?>
                                <?php if ($track->getErrorMessage()) : ?>
                                    <tr>
                                        <th class="col label" scope="row"><?= $block->escapeHtml(__('Error:')) ?></th>
                                        <td class="col error">
                                            <?= $block->escapeHtml(__('Tracking information is currently not available. Please ')) ?>
                                            <?php if ($block->getParentBlock() && $block->getParentBlock()->getContactUsEnabled()) : ?>
                                                <a href="<?= $block->escapeUrl($block->getParentBlock()->getContactUs()) ?>" target="_blank"
                                                   title="<?= $block->escapeHtml(__('contact us')) ?>">
                                                    <?= $block->escapeHtml(__('contact us')) ?>
                                                </a>
                                                <?= $block->escapeHtml(__(' for more information or ')) ?>
                                            <?php endif; ?>
                                            <?= $block->escapeHtml(__('email us at ')) ?>
                                            <a href="mailto:<?= /* @noEscape */ $block->getData('storeSupportEmail') ?>"><?= /* @noEscape */ $block->getData('storeSupportEmail') ?></a>
                                        </td>
                                    </tr>
                                <?php elseif ($track->getTrackSummary()) : ?>
                                    <tr>
                                        <th class="col label" scope="row"><?= $block->escapeHtml(__('Info:')) ?></th>
                                        <td class="col value"><?= $block->escapeHtml($track->getTrackSummary()) ?></td>
                                    </tr>
                                <?php elseif ($track->getUrl()) : ?>
                                    <tr>
                                        <th class="col label" scope="row"><?= $block->escapeHtml(__('Track:')) ?></th>
                                        <td class="col value">
                                            <a href="<?= $block->escapeUrl($track->getUrl()) ?>"
                                               target="_blank"
                                               class="text-primary underline hover:text-primary-dark"
                                               title="<?= $block->escapeHtml(__('Track Package')) ?>">
                                                <?= $block->escapeHtml(__('Track Package')) ?>
                                            </a>
                                        </td>
                                    </tr>
                                <?php else : ?>
                                    <?php foreach ($fields as $title => $property) : ?>
                                        <?php if (!empty($track->$property())) : ?>
                                            <tr>
                                                <th class="col label" scope="row"><?= /* @noEscape */ $block->escapeHtml(__($title . ':')) ?></th>
                                                <td class="col value"><?= $block->escapeHtml($track->$property()) ?></td>
                                            </tr>
                                        <?php endif;?>
                                    <?php endforeach; ?>

                                    <?php if ($track->getDeliverydate()) : ?>
                                        <tr>
                                            <th class="col label" scope="row"><?= $block->escapeHtml($block->getParentBlock() ? $block->getParentBlock()->getDeliveryDateTitle()->getTitle($track) : __('Delivered on:')) ?></th>
                                            <td class="col value">
                                                <?= /* @noEscape */ $block->getParentBlock() ? $block->getParentBlock()->formatDeliveryDateTime($track->getDeliverydate(), $track->getDeliverytime()) : $track->getDeliverydate() ?>
                                            </td>
                                        </tr>
                                    <?php endif; ?>
                                <?php endif; ?>
                            <?php elseif (isset($track['title']) && isset($track['number']) && $track['number']) : ?>
                                <?php /* if the tracking is custom value */ ?>
                                <tr>
                                    <th class="col label" scope="row">
                                        <?= ($track['title'] ? $block->escapeHtml($track['title']) : $block->escapeHtml(__('N/A'))) ?>:
                                    </th>
                                    <td class="col value"><?= (isset($track['number']) ? $block->escapeHtml($track['number']) : '') ?></td>
                                </tr>
                            <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php if (is_object($track) && !empty($track->getProgressdetail())): ?>
                        <?php
                            $block->addChild(
                                'shipping.tracking.progress.' . $shipId . '.' . $counter,
                                Template::class,
                                ['track' => $track, 'template' => 'Magento_Shipping::tracking/progress.phtml']
                            );
                        ?>
                        <?= /* @noEscape */ $block->getChildHtml('shipping.tracking.progress.' . $shipId . '.' . $counter) ?>
                    <?php endif; ?>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="message info empty">
                    <div><?= $block->escapeHtml(__('There is no tracking available for this shipment.')) ?></div>
                </div>
            <?php endif; ?>
        <?php endforeach; ?>
    <?php else: ?>
        <div class="message info empty">
            <div><?= $block->escapeHtml(__('There is no tracking available.')) ?></div>
        </div>
    <?php endif; ?>
    <div class="actions">
        <button type="button"
                title="<?= $block->escapeHtml(__('Close Window')) ?>"
                class="action close">
            <span><?= $block->escapeHtml(__('Close Window')) ?></span>
        </button>
        <?= /* @noEscape */ $secureRenderer->renderEventListenerAsTag(
            'onclick',
            "window.close(); window.opener.focus();",
            'button.action.close'
        ) ?>
    </div>
</div>
<?php $scriptString = <<<script

    require([
        'jquery'
    ], function (jQuery) {
        /* hide the close button when the content doesn't open in a modal window */
        if (window.opener === null || typeof window.opener === "undefined") {
            jQuery('.actions button.close').hide();
        }
    });

script;
?>
<?= /* @noEscape */ $secureRenderer->renderTag('script', [], $scriptString, false) ?>
