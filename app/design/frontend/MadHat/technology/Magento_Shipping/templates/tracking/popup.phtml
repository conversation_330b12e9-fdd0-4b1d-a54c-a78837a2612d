<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// phpcs:disable Magento2.Templates.ThisInTemplate

/** @var \Magento\Shipping\Block\Tracking\Popup $block */
/** @var \Magento\Framework\Escaper $escaper */

$trackingInfo = $block->getTrackingInfo();
?>
<div class="block-content">
    <?php if ($trackingInfo): ?>
        <?php foreach ($trackingInfo as $shipId => $_info): ?>
            <?php if ($_info['tracking']): ?>
                <div id="tracking-table-popup-<?= $escaper->escapeHtmlAttr($shipId) ?>">
                    <?php foreach ($_info['tracking'] as $_item): ?>
                        <div class="tracking-item mb-4">
                            <div class="tracking-header flex items-center justify-between mb-2">
                                <div class="tracking-carrier">
                                    <span class="inline-block pr-1 font-semibold">
                                        <?= $escaper->escapeHtml($_item['carrier_title']) ?>:
                                    </span>
                                    <span class="inline-block px-1">
                                        <?= $escaper->escapeHtml($_item['tracking']) ?>
                                    </span>
                                </div>
                                <?php if (!empty($_item['url'])): ?>
                                    <div class="tracking-link">
                                        <a href="<?= $escaper->escapeUrl($_item['url']) ?>" 
                                           target="_blank" 
                                           class="text-primary underline hover:text-primary-dark"
                                           title="<?= $escaper->escapeHtmlAttr(__('Track Package')) ?>">
                                            <?= $escaper->escapeHtml(__('Track Package')) ?>
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                            
                            <?php if (!empty($_item['status'])): ?>
                                <div class="tracking-status mb-2">
                                    <span class="font-medium"><?= $escaper->escapeHtml(__('Status')) ?>:</span>
                                    <span><?= $escaper->escapeHtml($_item['status']) ?></span>
                                </div>
                            <?php endif; ?>
                            
                            <?php if (!empty($_item['delivered_date'])): ?>
                                <div class="tracking-delivered mb-2">
                                    <span class="font-medium"><?= $escaper->escapeHtml(__('Delivered on')) ?>:</span>
                                    <span><?= $escaper->escapeHtml($_item['delivered_date']) ?></span>
                                </div>
                            <?php endif; ?>
                            
                            <?php if (!empty($_item['signed_by'])): ?>
                                <div class="tracking-signed mb-2">
                                    <span class="font-medium"><?= $escaper->escapeHtml(__('Signed by')) ?>:</span>
                                    <span><?= $escaper->escapeHtml($_item['signed_by']) ?></span>
                                </div>
                            <?php endif; ?>
                            
                            <?php if (!empty($_item['ship_date'])): ?>
                                <div class="tracking-shipped mb-2">
                                    <span class="font-medium"><?= $escaper->escapeHtml(__('Shipped on')) ?>:</span>
                                    <span><?= $escaper->escapeHtml($_item['ship_date']) ?></span>
                                </div>
                            <?php endif; ?>
                            
                            <?php if (!empty($_item['service'])): ?>
                                <div class="tracking-service mb-2">
                                    <span class="font-medium"><?= $escaper->escapeHtml(__('Service')) ?>:</span>
                                    <span><?= $escaper->escapeHtml($_item['service']) ?></span>
                                </div>
                            <?php endif; ?>
                            
                            <?php if (!empty($_item['weight'])): ?>
                                <div class="tracking-weight mb-2">
                                    <span class="font-medium"><?= $escaper->escapeHtml(__('Weight')) ?>:</span>
                                    <span><?= $escaper->escapeHtml($_item['weight']) ?></span>
                                </div>
                            <?php endif; ?>
                            
                            <?php if (!empty($_item['progressdetail'])): ?>
                                <div class="tracking-progress mt-4">
                                    <h4 class="font-semibold mb-2"><?= $escaper->escapeHtml(__('Tracking Progress')) ?></h4>
                                    <div class="progress-details">
                                        <?php foreach ($_item['progressdetail'] as $_detail): ?>
                                            <div class="progress-item border-l-2 border-gray-300 pl-4 pb-4 last:pb-0">
                                                <?php if (!empty($_detail['activity'])): ?>
                                                    <div class="activity font-medium">
                                                        <?= $escaper->escapeHtml($_detail['activity']) ?>
                                                    </div>
                                                <?php endif; ?>
                                                
                                                <?php if (!empty($_detail['deliverydate']) || !empty($_detail['deliverytime'])): ?>
                                                    <div class="delivery-info text-sm text-gray-600">
                                                        <?php if (!empty($_detail['deliverydate'])): ?>
                                                            <span><?= $escaper->escapeHtml($_detail['deliverydate']) ?></span>
                                                        <?php endif; ?>
                                                        <?php if (!empty($_detail['deliverytime'])): ?>
                                                            <span><?= $escaper->escapeHtml($_detail['deliverytime']) ?></span>
                                                        <?php endif; ?>
                                                    </div>
                                                <?php endif; ?>
                                                
                                                <?php if (!empty($_detail['deliverylocation'])): ?>
                                                    <div class="location text-sm text-gray-600">
                                                        <?= $escaper->escapeHtml($_detail['deliverylocation']) ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        <?php endforeach; ?>
    <?php else: ?>
        <div class="message info">
            <span><?= $escaper->escapeHtml(__('There is no tracking available for this shipment.')) ?></span>
        </div>
    <?php endif; ?>
</div>
