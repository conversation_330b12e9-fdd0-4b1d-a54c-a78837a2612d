<?php

namespace MadHat\SiteIntegrationShipping\Model\Order;

use MadHat\DbLogger\Logger\DbLoggerSaver;
use MadHat\DbLogger\Model\Config\Source\LogIdentifierProvider;
use MadHat\OrderIntegration\Api\MadhatOrderInfoRepositoryInterface;
use MadHat\SiteIntegrationCore\Model\Common;
use MadHat\SiteIntegrationMapping\Api\MappingRepositoryInterface;
use MadHat\SiteIntegrationShipping\Api\Data\ShippingInterface;
use MadHat\SiteIntegrationShipping\Api\Data\ShippingInterfaceFactory;
use MadHat\SiteIntegrationShipping\Api\ShippingRepositoryInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\DB\TransactionFactory;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Sales\Api\Data\OrderInterface;
use Magento\Sales\Api\Data\ShipmentCommentInterfaceFactory;
use Magento\Sales\Api\Data\ShipmentTrackInterfaceFactory;
use Magento\Sales\Api\ShipmentRepositoryInterface;
use Magento\Sales\Model\Convert\OrderFactory;
use Magento\Sales\Model\Order\Shipment\Sender\EmailSender;
use Psr\Log\LoggerInterface;

class ProductShipments
{
    const ARR_KEY_ORDERSTATUSDATA = 'OrderStatusData';
    const ARR_KEY_CONSIGNMENTS = 'Consignments';
    const ARR_KEY_PRODUCTSHIPMENTS = 'ProductShipments';
    const ARR_KEY_PRODUCTNO = 'ProductNo';
    const ARR_KEY_PICKLISTNO = 'PicklistNo';
    const ARR_KEY_DELIVERYGROUP = 'DeliveryGroup';
    const ARR_KEY_QUANTITY = 'Quantity';
    const ARR_KEY_SHIPMENTID = 'ShipmentId';
    const ARR_KEY_SCANNINGTIMESTAMP = 'ScanningTimestamp';
    const ARR_KEY_SHIPMENTMETHOD = 'ShipmentMethod';
    const ARR_KEY_SHIPMENTSERVICE = 'ShipmentService';
    const ARR_KEY_TRACKINGURL = 'TrackingUrl';

    /**
     * @var LoggerInterface
     */
    private LoggerInterface $logger;
    /**
     * @var ShipmentRepositoryInterface
     */
    private $shipmentRepository;
    /**
     * @var ShippingInterface
     */
    private $ipiccoloShipmentFactory;

    /**
     * @var SearchCriteriaBuilder
     */
    private SearchCriteriaBuilder $searchCriteriaBuilder;
    /**
     * @var ShippingRepositoryInterface
     */
    private ShippingRepositoryInterface $ipiccoloShipmentRepository;
    /**
     * @var ShipmentCommentInterfaceFactory
     */
    private ShipmentCommentInterfaceFactory $shipmentCommentInterfaceFactory;
    /**
     * @var ShipmentTrackInterfaceFactory
     */
    private $shipmentTrackInterfaceFactory;
    /**
     * @var TransactionFactory
     */
    private TransactionFactory $transactionFactory;
    /**
     * @var ShipmentSender
     */
    private $shipmentSender;
    /**
     * @var OrderFactory
     */
    private OrderFactory $convertOrderFactory;

    /**
     * @var \Magento\Sales\Model\Convert\Order
     */
    private \Magento\Sales\Model\Convert\Order $orderConverter;
    /**
     * @var MappingRepositoryInterface
     */
    private MappingRepositoryInterface $mappingRepository;
    /**
     * @var ResourceConnection
     */
    private $resourceConnection;
    /**
     * @var MadhatOrderInfoRepositoryInterface
     */
    private MadhatOrderInfoRepositoryInterface $madhatOrderInfoRepository;

    /**
     * @var DbLoggerSaver
     */
    private $dbLoggerSaver;
    /**
     * @var Common
     */
    private Common $common;

    /**
     * @param MadhatOrderInfoRepositoryInterface $madhatOrderInfoRepository
     * @param ResourceConnection $resourceConnection
     * @param MappingRepositoryInterface $mappingRepository
     * @param EmailSender $shipmentSender
     * @param TransactionFactory $transactionFactory
     * @param ShipmentTrackInterfaceFactory $shipmentTrackInterfaceFactory
     * @param ShipmentCommentInterfaceFactory $shipmentCommentInterfaceFactory
     * @param ShippingRepositoryInterface $ipiccoloShipmentRepository
     * @param SearchCriteriaBuilder $searchCriteriaBuilder
     * @param Common $common
     * @param OrderFactory $convertOrderFactory
     * @param ShippingInterfaceFactory $ipiccoloShipmentRepositoryFactory
     * @param ShipmentRepositoryInterface $shipmentRepository
     * @param LoggerInterface $logger
     * @param DbLoggerSaver $dbLoggerSaver
     */
    public function __construct(
        MadhatOrderInfoRepositoryInterface                      $madhatOrderInfoRepository,
        ResourceConnection                                      $resourceConnection,
        MappingRepositoryInterface                              $mappingRepository,
        EmailSender                                             $shipmentSender,
        TransactionFactory                                      $transactionFactory,
        \Magento\Sales\Api\Data\ShipmentTrackInterfaceFactory   $shipmentTrackInterfaceFactory,
        \Magento\Sales\Api\Data\ShipmentCommentInterfaceFactory $shipmentCommentInterfaceFactory,
        ShippingRepositoryInterface                             $ipiccoloShipmentRepository,
        SearchCriteriaBuilder                                   $searchCriteriaBuilder,
        Common                                                  $common,
        OrderFactory                                            $convertOrderFactory,
        ShippingInterfaceFactory                                $ipiccoloShipmentRepositoryFactory,
        ShipmentRepositoryInterface                             $shipmentRepository,
        LoggerInterface                                         $logger,
        DbLoggerSaver                                           $dbLoggerSaver
    ) {
        $this->madhatOrderInfoRepository = $madhatOrderInfoRepository;
        $this->resourceConnection = $resourceConnection;
        $this->mappingRepository = $mappingRepository;
        $this->shipmentSender = $shipmentSender;
        $this->transactionFactory = $transactionFactory;
        $this->shipmentTrackInterfaceFactory = $shipmentTrackInterfaceFactory;
        $this->shipmentCommentInterfaceFactory = $shipmentCommentInterfaceFactory;
        $this->ipiccoloShipmentRepository = $ipiccoloShipmentRepository;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->common = $common;
        $this->convertOrderFactory = $convertOrderFactory;
        $this->ipiccoloShipmentFactory = $ipiccoloShipmentRepositoryFactory;
        $this->shipmentRepository = $shipmentRepository;
        $this->logger = $logger;
        $this->dbLoggerSaver = $dbLoggerSaver;
    }

    /**
     * @param array $orderStatusData
     * @param OrderInterface $order
     * @return int
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    public function createShipment(array $orderStatusData, \Magento\Sales\Api\Data\OrderInterface $order): int
    {
        //        $magentoOrderId = $order->getEntityId();
        //        $madhatOrderInfo = $this->madhatOrderInfoRepository->getByOrderId((int)$magentoOrderId);
        //        $fcOrderId = $madhatOrderInfo->getFcOrderId();
        //        if(!empty($fcOrderId)) { //
        //            return $this->createShipmentWithoutProductShipments($orderStatusData, $order);
        //        }
        //        return $this->createShipmentWithProductShipments($orderStatusData, $order);
        return $this->createShipmentCommon($orderStatusData, $order);
    }

    /**
     * Create shipment common function that handles different API response structures
     * It replacement of functions createShipmentWithProductShipments and createShipmentWithoutProductShipments
     *
     * This function unifies shipment creation logic for different API response formats:
     * - Handles cases with or without ProductShipments data
     * - Validates Consignments (required - no shipment created if missing/empty)
     * - Processes PicklistNo (defaults to 0 if not provided)
     * - Ships specific products if ProductShipments exist, otherwise ships all order items
     * - Uses DbLogger for comprehensive logging with ORDER identifier
     *
     * API Response Examples:
     * 1. With ProductShipments: Ships only specified products with their quantities
     * 2. Without ProductShipments: Ships all available order items
     *
     * Business Rules:
     * - Consignments must exist and not be empty (validation fails otherwise)
     * - PicklistNo can be missing (defaults to 0)
     * - ProductShipments determines shipping scope:
     *   * Present: Ship only listed products with specified quantities
     *   * Missing/Empty: Ship all shippable order items
     * - Multiple tracking numbers supported from Consignments array
     * - Comprehensive logging at each step for debugging and monitoring
     *
     * @param array $orderStatusData API response data containing OrderStatusData with Consignments, ProductShipments, etc.
     * @param \Magento\Sales\Api\Data\OrderInterface $order Magento order object to create shipment for
     * @return int Shipment ID on success, 0 on failure (check DbLogger for detailed error info)
     * @throws \Magento\Framework\Exception\LocalizedException When shipment creation fails due to Magento constraints
     */
    public function createShipmentCommon(array $orderStatusData, \Magento\Sales\Api\Data\OrderInterface $order): int
    {
        // STEP 1: Validate Consignments (REQUIRED)
        // Consignments contain tracking information and are mandatory for shipment creation
        $consignments = $this->processingConsignments($orderStatusData, $order);
        if (empty($consignments)) {
            $this->dbLoggerSaver->addRecord(
                'Shipment Creation Failed',
                "Order #{$order->getIncrementId()}[{$order->getEntityId()}] - Consignments not exist or empty",
                'WARNING',
                LogIdentifierProvider::ORDER
            );
            return 0; // Exit early - cannot create shipment without tracking info
        }

        // STEP 2: Extract PicklistNo (OPTIONAL - defaults to 0)
        // PicklistNo is used for shipment comments and internal tracking
        $picklistNo = $this->processingPicklistNo($orderStatusData);

        // STEP 3: Extract ProductShipments (OPTIONAL)
        // Determines shipping scope: specific products vs all order items
        $productShipments = $this->processingProductShipments($orderStatusData);

        // Log the shipment creation attempt with key parameters
        $this->dbLoggerSaver->addRecord(
            'Shipment Creation Started',
            "Order #{$order->getIncrementId()}[{$order->getEntityId()}] - PicklistNo: {$picklistNo}, ProductShipments: " . (empty($productShipments) ? 'No' : 'Yes'),
            'INFO',
            LogIdentifierProvider::ORDER
        );

        // STEP 4: Validate Order Shippability
        // Magento-level check to ensure order can be shipped (not already shipped, cancelled, etc.)
        if (!$order->canShip()) {
            $this->dbLoggerSaver->addRecord(
                'Shipment Creation Failed',
                "Order #{$order->getIncrementId()}[{$order->getEntityId()}] - Order cannot be shipped (canShip = false)",
                'WARNING',
                LogIdentifierProvider::ORDER
            );
            return 0; // Exit early - Magento prevents shipment creation
        }

        // STEP 5: Initialize Magento Shipment Object
        // Create the shipment container that will hold all shipped items
        $this->orderConverter = $this->convertOrderFactory->create();
        $shipment = $this->orderConverter->toShipment($order);

        // Track what items are added for validation and logging
        $itemsAdded = false;
        $itemCount = 0;

        // STEP 6: Add Items to Shipment Based on ProductShipments Data
        // Two scenarios: Specific products (ProductShipments exist) vs All products (ProductShipments empty)
        if (!empty($productShipments)) {
            // SCENARIO A: Ship only specific products listed in ProductShipments
            // This is used when API specifies exactly which products and quantities to ship
            foreach ($productShipments as $item) {
                // Prevent duplicate shipments by checking if this product was already shipped
                if ($this->checkProductShipmentsItem($item, $orderStatusData, $order)) {
                    // Find matching order items by SKU and add them to shipment
                    foreach ($order->getAllItems() as $orderItem) {
                        // Skip items that cannot be shipped (already shipped, virtual products, etc.)
                        if (!$orderItem->getQtyToShip() || $orderItem->getIsVirtual()) {
                            continue;
                        }
                        // Match order item SKU with ProductShipments ProductNo
                        if ($orderItem->getSku() == $item[self::ARR_KEY_PRODUCTNO]) {
                            $qtyShipped = $item[self::ARR_KEY_QUANTITY]; // Use API-specified quantity
                            // Create shipment item with specified quantity
                            $shipmentItem = $this->orderConverter->itemToShipmentItem($orderItem)->setQty($qtyShipped);
                            $shipment->addItem($shipmentItem);
                            $itemsAdded = true;
                            $itemCount++;
                        }
                    }
                }
            }
        } else {
            // SCENARIO B: Ship all available order items
            // This is used when API doesn't specify ProductShipments (ship everything)
            foreach ($order->getAllItems() as $orderItem) {
                // Skip items that cannot be shipped (already shipped, virtual products, etc.)
                if (!$orderItem->getQtyToShip() || $orderItem->getIsVirtual()) {
                    continue;
                }

                $qtyShipped = $orderItem->getQtyToShip(); // Use all remaining quantity
                // Create shipment item with remaining quantity
                $shipmentItem = $this->orderConverter->itemToShipmentItem($orderItem)->setQty($qtyShipped);
                $shipment->addItem($shipmentItem);
                $itemsAdded = true;
                $itemCount++;
            }
        }

        // STEP 7: Validate Items Were Added
        // Ensure at least one item was added to the shipment before proceeding
        if (!$itemsAdded) {
            $this->dbLoggerSaver->addRecord(
                'Shipment Creation Failed',
                "Order #{$order->getIncrementId()}[{$order->getEntityId()}] - No items added to shipment, PicklistNo: {$picklistNo}",
                'WARNING',
                LogIdentifierProvider::ORDER
            );
            return 0; // Exit early - empty shipment is invalid
        }

        // STEP 8: Add Tracking Information
        // Process all consignments to create tracking numbers for the shipment
        foreach ($consignments as $consignment) {
            $shipmentTrack = $this->shipmentTrackInterfaceFactory->create();
            $shipmentTrack->setTrackNumber($consignment[self::ARR_KEY_SHIPMENTID]); // Tracking number from API
            $shipmentTrack->setCarrierCode($this->getCarrierCode($order)); // Carrier mapping
            $shipmentTrack->setTitle($consignment[self::ARR_KEY_SHIPMENTSERVICE]); // Shipping method name
            // Set tracking URL to allow "Shipping and Tracking Information" link in Magento
            if (!empty($consignment[self::ARR_KEY_TRACKINGURL])) {
                $shipmentTrack->setUrl($consignment[self::ARR_KEY_TRACKINGURL]);
            }
            $shipment->addTrack($shipmentTrack);
        }

        // STEP 9: Add Internal Comment
        // Add PicklistNo as internal comment for reference and debugging
        $shipmentComment = $this->shipmentCommentInterfaceFactory->create();
        $shipmentComment->setIsVisibleOnFront(0); // Internal comment only
        $shipmentComment->setComment(__('PicklistNo: %1', $picklistNo));
        $shipment->addComment($shipmentComment);

        // STEP 10: Prepare Shipment for Saving
        // Register the shipment and mark order as in process
        $shipment->register(); // Finalizes shipment data
        $shipment->getOrder()->setIsInProcess(true); // Updates order status

        // STEP 11: Save Shipment to Database
        try {
            // Use database transaction to ensure data consistency
            $this->transactionFactory->create()->addObject($shipment)->addObject($shipment->getOrder())->save();
            $shipmentId = (int)$shipment->getId();

            // STEP 12: Save Additional ProductShipments Data (if applicable)
            // Store detailed product shipment information for tracking and reporting
            if (!empty($productShipments)) {
                foreach ($productShipments as $item) {
                    $this->ipiccoloShipmentSave($item, $orderStatusData, $order, $shipmentId);
                }
            }

            // STEP 13: Log Successful Creation
            $this->dbLoggerSaver->addRecord(
                'Shipment Created Successfully',
                "Order #{$order->getIncrementId()}[{$order->getEntityId()}] - Shipment ID: {$shipmentId}, Items: {$itemCount}, PicklistNo: {$picklistNo}",
                'INFO',
                LogIdentifierProvider::ORDER
            );

            return $shipmentId; // Return the created shipment ID for further processing
        } catch (\Exception $e) {
            // STEP 14: Handle and Log Errors
            // Log detailed error information for debugging
            $this->dbLoggerSaver->addRecord(
                'Shipment Creation Error',
                "Order #{$order->getIncrementId()}[{$order->getEntityId()}] - Error: {$e->getMessage()}",
                'ERROR',
                LogIdentifierProvider::ORDER
            );
            return 0; // Return 0 to indicate failure
        }
    }

    /**
     * Process Consignments from API response
     *
     * Consignments contain tracking information (ShipmentId, ShipmentMethod, etc.)
     * and are REQUIRED for shipment creation. If missing or empty, shipment creation fails.
     *
     * Expected structure: OrderStatusData.Consignments[]
     * Each consignment contains: PicklistNo, ShipmentId, ShipmentMethod, ScanningTimestamp (optional)
     *
     * @param array $orderStatusData Complete API response data
     * @param \Magento\Sales\Api\Data\OrderInterface $order Order object (used for potential future enhancements)
     * @return array Array of consignments or empty array if not found/invalid
     */
    private function processingConsignments(array $orderStatusData, \Magento\Sales\Api\Data\OrderInterface $order): array
    {
        if (!$this->common->arrayKeyExistsMulti($orderStatusData, self::ARR_KEY_CONSIGNMENTS)) {
            return [];
        }

        $consignments = $orderStatusData[self::ARR_KEY_ORDERSTATUSDATA][self::ARR_KEY_CONSIGNMENTS];

        if (empty($consignments) || !is_array($consignments)) {
            return [];
        }

        return $consignments;
    }

    /**
     * Process PicklistNo from API response
     *
     * PicklistNo is used for internal tracking and shipment comments.
     * It's OPTIONAL - defaults to 0 if not provided in the API response.
     * Extracted from the first consignment in the array.
     *
     * Expected location: OrderStatusData.Consignments[0].PicklistNo
     *
     * @param array $orderStatusData Complete API response data
     * @return int PicklistNo value or 0 if not found
     */
    private function processingPicklistNo(array $orderStatusData): int
    {
        if (!$this->common->arrayKeyExistsMulti($orderStatusData, self::ARR_KEY_CONSIGNMENTS)) {
            return 0;
        }

        $consignments = $orderStatusData[self::ARR_KEY_ORDERSTATUSDATA][self::ARR_KEY_CONSIGNMENTS];

        if (empty($consignments) || !is_array($consignments)) {
            return 0;
        }

        // Get PicklistNo from first consignment, default to 0 if not set
        return isset($consignments[0][self::ARR_KEY_PICKLISTNO]) ? (int)$consignments[0][self::ARR_KEY_PICKLISTNO] : 0;
    }

    /**
     * Process ProductShipments from API response
     *
     * ProductShipments determine the SCOPE of shipment creation:
     * - PRESENT: Ship only the specified products with their exact quantities
     * - MISSING/EMPTY: Ship all available order items with their full quantities
     *
     * This allows for partial shipments (when ProductShipments specify subset of order)
     * or full shipments (when ProductShipments is empty/missing).
     *
     * Expected structure: OrderStatusData.ProductShipments[]
     * Each item contains: PicklistNo, DeliveryGroup, ProductNo, Quantity
     *
     * @param array $orderStatusData Complete API response data
     * @return array Array of product shipments or empty array if not found (triggers full shipment)
     */
    private function processingProductShipments(array $orderStatusData): array
    {
        if (!$this->common->arrayKeyExistsMulti($orderStatusData, self::ARR_KEY_PRODUCTSHIPMENTS)) {
            return [];
        }

        $productShipments = $orderStatusData[self::ARR_KEY_ORDERSTATUSDATA][self::ARR_KEY_PRODUCTSHIPMENTS];

        if (empty($productShipments) || !is_array($productShipments)) {
            return [];
        }

        return $productShipments;
    }

    /**
     * @param array $orderStatusData
     * @param \Magento\Sales\Api\Data\OrderInterface $order
     * @return int Shipment ID on success, 0 on failure
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    private function createShipmentWithProductShipments($orderStatusData, $order)
    {
        /* check shipment exist for order or not */
        if ($order->canShip()) {
            $deliveryGrouped = $this->getDeliveryGrouped($orderStatusData);
            foreach ($deliveryGrouped as $picklistNo => $itemForShipping) {
                // If Consignments not exist shipping cannot be saved;
                if (!isset($itemForShipping[self::ARR_KEY_CONSIGNMENTS])) {
                    $this->logger->warning(
                        __(
                            '%1 => %2[%3] Order #%4[%5] can not be shipped. Consignments for picklist %6 not exist',
                            __CLASS__,
                            __FUNCTION__,
                            __LINE__,
                            $order->getIncrementId(),
                            $order->getEntityId(),
                            $picklistNo
                        )
                    );
                    continue;
                }
                // Initialize the order shipment object
                $this->orderConverter = $this->convertOrderFactory->create();
                $shipment = $this->orderConverter->toShipment($order);
                if (isset($itemForShipping[self::ARR_KEY_PRODUCTSHIPMENTS])) {
                    $productShipments = $itemForShipping[self::ARR_KEY_PRODUCTSHIPMENTS];
                    foreach ($productShipments as $item) {
                        // Check if shipping not saved before
                        if ($this->checkProductShipmentsItem($item, $orderStatusData, $order)) {
                            foreach ($order->getAllItems() as $orderItem) { // order items processing
                                // Check if order item has qty to ship or is order is virtual
                                if (!$orderItem->getQtyToShip() || $orderItem->getIsVirtual()) {
                                    continue;
                                }
                                if ($orderItem->getSku() == $item[self::ARR_KEY_PRODUCTNO]) {
                                    $qtyShipped = $item[self::ARR_KEY_QUANTITY];
                                    // Create shipment item with qty
                                    $shipmentItem = $this->orderConverter->itemToShipmentItem($orderItem)->setQty($qtyShipped);
                                    // Add shipment item to shipment
                                    $shipment->addItem($shipmentItem);
                                }
                            }
                        }
                    }
                }
                // add Track numbers
                $consignments = $itemForShipping[self::ARR_KEY_CONSIGNMENTS];
                foreach ($consignments as $consignment) {
                    $shipmentTrack = $this->shipmentTrackInterfaceFactory->create();
                    $shipmentTrack->setTrackNumber($consignment[self::ARR_KEY_SHIPMENTID]);
                    $shipmentTrack->setCarrierCode($this->getCarrierCode($order));
                    $shipmentTrack->setTitle($consignment[self::ARR_KEY_SHIPMENTSERVICE]);
                    $shipment->addTrack($shipmentTrack);
                }
                // add Comment
                $shipmentComment = $this->shipmentCommentInterfaceFactory->create();
                $shipmentComment->setIsVisibleOnFront(0);
                $shipmentComment->setComment(__('PicklistNo: %1', $picklistNo));
                $shipment->addComment($shipmentComment);
                // save Shipping
                // Register shipment
                $shipment->register();
                $shipment->getOrder()->setIsInProcess(true);
                try {
                    $this->transactionFactory->create()->addObject($shipment)->addObject($shipment->getOrder())->save();
                    $shipmentId = (int)$shipment->getId();
                    foreach ($productShipments as $item) {
                        $this->ipiccoloShipmentSave($item, $orderStatusData, $order, $shipmentId);
                    }
                    return $shipmentId; // Return shipment ID on success
                } catch (\Exception $e) {
                    $this->logger->error(
                        __(
                            '%1 => %2[%3] Magento shipment generate fail. Error: %4',
                            __CLASS__,
                            __FUNCTION__,
                            __LINE__,
                            $e->getMessage()
                        )
                    );
                    return 0; // Return 0 on failure
                }
            }
        } else {
            $this->logger->warning(
                __(
                    '%1 => %2[%3] Order #%4[%5] can not be shipped (canShip = false)',
                    __CLASS__,
                    __FUNCTION__,
                    __LINE__,
                    $order->getIncrementId(),
                    $order->getEntityId()
                )
            );
        }
        return 0; // Return 0 if shipment cannot be created
    }

    /**
     * Create shipment without product shipments
     *
     * @param $orderStatusData
     * @param $order
     * @return int Shipment ID on success, 0 on failure
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    private function createShipmentWithoutProductShipments($orderStatusData, $order): int
    {
        if (!$this->common->arrayKeyExistsMulti($orderStatusData, self::ARR_KEY_CONSIGNMENTS)) {
            $this->logger->debug(
                __(
                    '%1 => %2[%3] %6 not exists for Order #%4[%5]',
                    __CLASS__,
                    __FUNCTION__,
                    __LINE__,
                    $order->getIncrementId(),
                    $order->getEntityId(),
                    self::ARR_KEY_CONSIGNMENTS
                )
            );
            return 0;
        }
        $this->logger->debug(
            __(
                '%1 => %2[%3] Started shipment creation for Order #%4[%5]',
                __CLASS__,
                __FUNCTION__,
                __LINE__,
                $order->getIncrementId(),
                $order->getEntityId()
            )
        );

        /* check shipment exist for order or not */
        if ($order->canShip()) {
            $this->logger->debug(
                __(
                    '%1 => %2[%3] Order #%4 can be shipped, proceeding',
                    __CLASS__,
                    __FUNCTION__,
                    __LINE__,
                    $order->getIncrementId()
                )
            );
            // Only one Consigments:
            $picklistNo = $orderStatusData[self::ARR_KEY_ORDERSTATUSDATA][self::ARR_KEY_CONSIGNMENTS][0][self::ARR_KEY_PICKLISTNO];
            $this->logger->debug(
                __(
                    '%1 => %2[%3] Processing picklist %4',
                    __CLASS__,
                    __FUNCTION__,
                    __LINE__,
                    $picklistNo
                )
            );

            // Initialize the order shipment object
            $this->orderConverter = $this->convertOrderFactory->create();
            $shipment = $this->orderConverter->toShipment($order);

            $itemsAdded = false;
            $itemCount = 0;

            // Add all shippable items regardless of ProductShipments
            foreach ($order->getAllItems() as $orderItem) {
                // Check if order item has qty to ship or is virtual
                if (!$orderItem->getQtyToShip() || $orderItem->getIsVirtual()) {
                    $this->logger->debug(
                        __(
                            '%1 => %2[%3] Skipping item %4 (id: %5): QtyToShip=%6, IsVirtual=%7',
                            __CLASS__,
                            __FUNCTION__,
                            __LINE__,
                            $orderItem->getName(),
                            $orderItem->getItemId(),
                            $orderItem->getQtyToShip(),
                            $orderItem->getIsVirtual() ? 'Yes' : 'No'
                        )
                    );
                    continue;
                }

                $qtyShipped = $orderItem->getQtyToShip();
                // Create shipment item with qty
                $shipmentItem = $this->orderConverter->itemToShipmentItem($orderItem)->setQty($qtyShipped);
                // Add shipment item to shipment
                $shipment->addItem($shipmentItem);
                $itemsAdded = true;
                $itemCount++;

                $this->logger->debug(
                    __(
                        '%1 => %2[%3] Added item %4 (id: %5) to shipment, qty: %6',
                        __CLASS__,
                        __FUNCTION__,
                        __LINE__,
                        $orderItem->getName(),
                        $orderItem->getItemId(),
                        $qtyShipped
                    )
                );
            }

            // Check if any items were added to the shipment
            if (!$itemsAdded) {
                $this->logger->warning(
                    __(
                        '%1 => %2[%3] No items added to shipment for Order #%4[%5], picklist %6. Skipping shipment creation.',
                        __CLASS__,
                        __FUNCTION__,
                        __LINE__,
                        $order->getIncrementId(),
                        $order->getEntityId(),
                        $picklistNo
                    )
                );
                return 0;
            }

            $this->logger->debug(
                __(
                    '%1 => %2[%3] Added %4 items to shipment for Order #%5',
                    __CLASS__,
                    __FUNCTION__,
                    __LINE__,
                    $itemCount,
                    $order->getIncrementId()
                )
            );

            // Only one Consigment
            $trackNumber = $orderStatusData[self::ARR_KEY_ORDERSTATUSDATA][self::ARR_KEY_CONSIGNMENTS][0][self::ARR_KEY_SHIPMENTID];
            $trackTitle = $orderStatusData[self::ARR_KEY_ORDERSTATUSDATA][self::ARR_KEY_CONSIGNMENTS][0][self::ARR_KEY_SHIPMENTSERVICE];

            $shipmentTrack = $this->shipmentTrackInterfaceFactory->create();
            $shipmentTrack->setTrackNumber($trackNumber);
            $shipmentTrack->setCarrierCode($this->getCarrierCode($order));
            $shipmentTrack->setTitle($trackTitle);
            if ($this->common->arrayKeyExistsMulti($orderStatusData, self::ARR_KEY_CONSIGNMENTS, 0, self::ARR_KEY_TRACKINGURL) &&
                !empty($orderStatusData[self::ARR_KEY_ORDERSTATUSDATA][self::ARR_KEY_CONSIGNMENTS][0][self::ARR_KEY_TRACKINGURL])) {
                $shipmentTrack->setUrl($orderStatusData[self::ARR_KEY_ORDERSTATUSDATA][self::ARR_KEY_CONSIGNMENTS][0][self::ARR_KEY_TRACKINGURL]);
            }
            $shipment->addTrack($shipmentTrack);
            $this->logger->debug(
                __(
                    '%1 => %2[%3] Added tracking %4 with carrier %5 to shipment',
                    __CLASS__,
                    __FUNCTION__,
                    __LINE__,
                    $trackNumber,
                    $trackTitle
                )
            );
            // add Comment
            $shipmentComment = $this->shipmentCommentInterfaceFactory->create();
            $shipmentComment->setIsVisibleOnFront(0);
            $shipmentComment->setComment(__('PicklistNo: %1', $picklistNo));
            $shipment->addComment($shipmentComment);

            $this->logger->debug(
                __(
                    '%1 => %2[%3] Added comment: PicklistNo: %4',
                    __CLASS__,
                    __FUNCTION__,
                    __LINE__,
                    $picklistNo
                )
            );

            // Register shipment
            $shipment->register();
            $shipment->getOrder()->setIsInProcess(true);

            $this->logger->debug(
                __(
                    '%1 => %2[%3] Shipment registered, order set to in process',
                    __CLASS__,
                    __FUNCTION__,
                    __LINE__
                )
            );

            try {
                $this->logger->debug(
                    __(
                        '%1 => %2[%3] Attempting to save shipment',
                        __CLASS__,
                        __FUNCTION__,
                        __LINE__
                    )
                );

                $this->transactionFactory->create()->addObject($shipment)->addObject($shipment->getOrder())->save();
                $shipmentId = $shipment->getId();

                $this->logger->debug(
                    __(
                        '%1 => %2[%3] Shipment saved successfully with ID: %4',
                        __CLASS__,
                        __FUNCTION__,
                        __LINE__,
                        $shipmentId
                    )
                );

                // Still update product shipments data if available
                if (isset($itemForShipping[self::ARR_KEY_PRODUCTSHIPMENTS])) {
                    $productShipments = $itemForShipping[self::ARR_KEY_PRODUCTSHIPMENTS];
                    foreach ($productShipments as $item) {
                        $this->logger->debug(
                            __(
                                '%1 => %2[%3] Saving product shipment data',
                                __CLASS__,
                                __FUNCTION__,
                                __LINE__
                            )
                        );

                        $this->ipiccoloShipmentSave($item, $orderStatusData, $order, $shipmentId);
                    }
                }

                return (int)$shipmentId;
            } catch (\Exception $e) {
                $this->logger->error(
                    __(
                        '%1 => %2[%3] Magento shipment generate fail. Error: %4',
                        __CLASS__,
                        __FUNCTION__,
                        __LINE__,
                        $e->getMessage()
                    )
                );

                $this->logger->debug(
                    __(
                        '%1 => %2[%3] Exception stack trace: %4',
                        __CLASS__,
                        __FUNCTION__,
                        __LINE__,
                        $e->getTraceAsString()
                    )
                );

                return 0;
            }
        } else {
            $this->logger->warning(
                __(
                    '%1 => %2[%3] Order #%4[%5] can not be shipped (canShip = false)',
                    __CLASS__,
                    __FUNCTION__,
                    __LINE__,
                    $order->getIncrementId(),
                    $order->getEntityId()
                )
            );
        }

        return 0; // Return 0 if no shipment was created
    }

    /**
     * @param array $item
     * @param array $orderStatusData
     * @param \Magento\Sales\Api\Data\OrderInterface $order
     * @return false|ShippingInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    private function checkProductShipmentsItem($item, $orderStatusData, $order)
    {
        $searchCriteria = $this->searchCriteriaBuilder
            ->addFilter(ShippingInterface::PRODUCT_NO, $item[self::ARR_KEY_PRODUCTNO])
            ->addFilter(ShippingInterface::PICKLIST_NO, $item[self::ARR_KEY_PICKLISTNO])
            ->create();
        if (empty($this->ipiccoloShipmentRepository->getList($searchCriteria)->getItems())) {
            $this->logger->info(
                __(
                    '%1 => %2[%3] shipping for PicklistNo %4 ProductNo %5 not exists',
                    __CLASS__,
                    __FUNCTION__,
                    __LINE__,
                    $item[self::ARR_KEY_PICKLISTNO],
                    $item[self::ARR_KEY_PRODUCTNO]
                )
            );
            return true;
        }
        $this->logger->warning(
            __(
                '%1 => %2[%3] shipping for PicklistNo %4 ProductNo %5 already exists',
                __CLASS__,
                __FUNCTION__,
                __LINE__,
                $item[self::ARR_KEY_PICKLISTNO],
                $item[self::ARR_KEY_PRODUCTNO]
            )
        );
        return false;
    }

    /**
     * @param array $item
     * @param array $orderStatusData
     * @param \Magento\Sales\Api\Data\OrderInterface $order
     * @param int $shipmentId
     * @return false|ShippingInterface
     */
    private function ipiccoloShipmentSave($item, $orderStatusData, $order, $shipmentId = 0)
    {
        $ipiccoloShipment = $this->ipiccoloShipmentFactory->create();
        $ipiccoloShipment->setPicklistNo($item[self::ARR_KEY_PICKLISTNO]);
        $ipiccoloShipment->setProductNo($item[self::ARR_KEY_PRODUCTNO]);
        $ipiccoloShipment->setDeliveryGroup($item[self::ARR_KEY_DELIVERYGROUP]);
        $ipiccoloShipment->setQuantity($item[self::ARR_KEY_QUANTITY]);
        if ($shipmentId > 0) {
            $ipiccoloShipment->setSalesShipmentId($shipmentId);
        }
        if ($this->common->arrayKeyExistsMulti($orderStatusData, self::ARR_KEY_CONSIGNMENTS)) {
            $shipmentIdArr = [];
            $scanningTimestampArr = [];
            $shipmentMethodArr = [];
            foreach ($orderStatusData[self::ARR_KEY_ORDERSTATUSDATA][self::ARR_KEY_CONSIGNMENTS] as $consignments) {
                if ($consignments[self::ARR_KEY_PICKLISTNO] == $item[self::ARR_KEY_PICKLISTNO]) {
                    $shipmentIdArr[] = $consignments[self::ARR_KEY_SHIPMENTID];
                    $scanningTimestampArr[] = $consignments[self::ARR_KEY_SCANNINGTIMESTAMP];
                    $shipmentMethodArr[] = $consignments[self::ARR_KEY_SHIPMENTMETHOD];
                }
            }
            $shipmentId = implode(',', array_unique($shipmentIdArr));
            $scanningTimestamp = implode(',', array_unique($scanningTimestampArr));
            $shipmentMethod = implode(',', array_unique($shipmentMethodArr));
            $ipiccoloShipment->setShipmentId($shipmentId);
            $ipiccoloShipment->setScanningTimestamp($scanningTimestamp);
            $ipiccoloShipment->setShipmentMethod($shipmentMethod);
            $ipiccoloShipment->setOrderId($order->getEntityId());
            $ipiccoloShipment->setIncrementId($order->getIncrementId());
            try {
                return $this->ipiccoloShipmentRepository->save($ipiccoloShipment);
            } catch (\Exception $e) {
                $this->logger->error(
                    __(
                        '%1 => %2[%3] ipiccolo shipment save fail. Error: %4',
                        __CLASS__,
                        __FUNCTION__,
                        __LINE__,
                        $e->getMessage()
                    )
                );
                return false;
            }
        } else {
            $this->logger->warning(
                __(
                    '%1 => %2[%3] Array key %4 not exists',
                    __CLASS__,
                    __FUNCTION__,
                    __LINE__,
                    self::ARR_KEY_CONSIGNMENTS
                )
            );
            return false;
        }
    }

    /**
     * @param string $shipmentmethod
     * @param \Magento\Sales\Api\Data\OrderInterface $order
     * @return string
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getCarrierCode($order, $shipmentmethod = '')
    {
        if (!empty($shipmentmethod)) {
            return ($this->mapping($shipmentmethod)) ? $this->mapping($shipmentmethod) : 'custom';
        }
        return !empty($order->getData('shipping_method')) ? $order->getData('shipping_method') : 'custom';
    }

    /**
     * @param array $orderStatusData
     * @return array
     */
    private function getDeliveryGrouped(array $orderStatusData)
    {
        $deliveryGrouped = [];
        if ($this->common->arrayKeyExistsMulti($orderStatusData, self::ARR_KEY_PRODUCTSHIPMENTS)) {
            $productShipments = $orderStatusData[self::ARR_KEY_ORDERSTATUSDATA][self::ARR_KEY_PRODUCTSHIPMENTS];
            foreach ($productShipments as $item) {
                $deliveryGrouped[$item[self::ARR_KEY_PICKLISTNO]][self::ARR_KEY_PRODUCTSHIPMENTS][] = $item;
            }

        }
        if ($this->common->arrayKeyExistsMulti($orderStatusData, self::ARR_KEY_CONSIGNMENTS)) {
            $consignments = $orderStatusData[self::ARR_KEY_ORDERSTATUSDATA][self::ARR_KEY_CONSIGNMENTS];
            foreach ($consignments as $item) {
                $deliveryGrouped[$item[self::ARR_KEY_PICKLISTNO]][self::ARR_KEY_CONSIGNMENTS][] = $item;
            }
        }
        return $deliveryGrouped;
    }

    /**
     * @param $type
     * @param $source
     * @return string
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function mapping($source)
    {
        $type = 'ShipmentMethod';
        $destination = [];
        $searchCriteria = $this->searchCriteriaBuilder
            ->addFilter('type', $type)
            ->addFilter('source', $source)->create();
        $items = $this->mappingRepository->getList($searchCriteria)->getItems();
        foreach ($items as $item) {
            $destination[] = $item->getDestination();
        }
        return implode(',', $destination);
    }
}
